import { useState, useRef, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Link, useNavigate, useLocation } from "react-router-dom";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import {
  ChevronRight,
  ChevronLeft,
  Thermometer,
  FileText,
  Mail,
  ArrowRight,
  ZoomIn,
  Eye,
  Zap,
  Shield,
  Activity,
  BarChart3,
  Search,
  Camera,
  Battery,
  Flame,
  X,
  Check,
  Grid
} from "lucide-react";

// PDF URL for brochure
const PDF_URL = "/T&M April 2025.pdf";

// Product Overview Card Component
const ProductCard = ({
  title,
  modelNumber,
  image,
  displayInfo,
  onViewDetailsClick
}: {
  title: string;
  modelNumber: string;
  image: string;
  displayInfo: string;
  onViewDetailsClick: () => void;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white border border-yellow-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 flex flex-col h-full"
      style={{ fontFamily: 'Open Sans, sans-serif' }}
    >
      {/* Model Number Badge */}
      <div className="flex justify-end p-3">
        <span className="bg-yellow-100 text-yellow-800 text-xs font-semibold px-3 py-1 rounded-full">
          {modelNumber}
        </span>
      </div>

      {/* Product Image */}
      <div className="flex-1 flex items-center justify-center p-4 bg-gray-50 mx-4 rounded-lg">
        <img
          src={image}
          alt={title}
          className="w-full h-48 object-contain"
          style={{ minHeight: '200px' }}
        />
      </div>

      {/* Product Info */}
      <div className="p-4 space-y-3">
        <h3 className="text-lg font-bold text-gray-900 text-center">{title}</h3>
        <div className="text-center">
          <span className="text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
            {displayInfo}
          </span>
        </div>

        {/* View Details Button */}
        <div className="pt-2">
          <Button
            onClick={onViewDetailsClick}
            className="w-full bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold py-2 px-4 rounded-lg transition-colors duration-200"
          >
            View Details
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

// Simplified thermal imager products data
const thermalImagers = [
  {
    id: "tc-s030",
    title: "Entry Level Thermal Imager",
    modelNumber: "TC S030",
    image: "/thermal-measurement/TC-S030.png",
    displayInfo: "96 × 96 IR Resolution"
  },
  {
    id: "tc-e050",
    title: "Pocket Thermal Imager",
    modelNumber: "TC E050",
    image: "/thermal-measurement/TC-E050.png",
    displayInfo: "96 × 96 IR Resolution"
  },
  {
    id: "ma-250",
    title: "Mobile Thermal Imager",
    modelNumber: "MA 250",
    image: "/thermal-measurement/ma-250.png",
    displayInfo: "256 × 192 IR Resolution"
  },
  {
    id: "tc-2150",
    title: "Compact Thermal Imager",
    modelNumber: "TC 2150",
    image: "/thermal-measurement/TC-2150.png",
    displayInfo: "192 × 144 IR Resolution"
  },
  {
    id: "tc-2250",
    title: "Enhanced Compact Imager",
    modelNumber: "TC 2250",
    image: "/thermal-measurement/TC-2250.png",
    displayInfo: "256 × 192 IR Resolution"
  },
  {
    id: "tc-s240",
    title: "Touch Screen Imager",
    modelNumber: "TC S240",
    image: "/thermal-measurement/TC-S240.png",
    displayInfo: "256 × 192 IR Resolution"
  },
  {
    id: "tc-3151",
    title: "Professional Thermal Imager",
    modelNumber: "TC 3151",
    image: "/thermal-measurement/TC-3150.png",
    displayInfo: "192 × 144 IR Resolution"
  },
  {
    id: "tc-3250",
    title: "Manual Focus Imager",
    modelNumber: "TC 3250",
    image: "/thermal-measurement/TC-3250.png",
    displayInfo: "256 × 192 IR Resolution"
  },
  {
    id: "tc-3360",
    title: "Wide Field Imager",
    modelNumber: "TC 3360",
    image: "/thermal-measurement/TC-3250.png",
    displayInfo: "384 × 288 IR Resolution"
  },
  {
    id: "tc-p360",
    title: "Pistol Grip Imager",
    modelNumber: "TC P360",
    image: "/thermal-measurement/tc-p360.png",
    displayInfo: "384 × 288 IR Resolution"
  },
  {
    id: "tc-4360",
    title: "High Resolution Imager",
    modelNumber: "TC 4360",
    image: "/thermal-measurement/TC-4360.png",
    displayInfo: "384 × 288 IR Resolution"
  },
  {
    id: "tc-4460",
    title: "Extended Range Imager",
    modelNumber: "TC 4460 / TC 4460H",
    image: "/thermal-measurement/TC-4460.png",
    displayInfo: "480 × 360 IR Resolution"
  },
  {
    id: "tc-3660",
    title: "Advanced Thermal Imager",
    modelNumber: "TC 3660",
    image: "/thermal-measurement/TC-3650.png",
    displayInfo: "640 × 480 IR Resolution"
  },
  {
    id: "tc-4660",
    title: "High-End Thermal Imager",
    modelNumber: "TC 4660 / TC 4660H",
    image: "/thermal-measurement/TC-4660.png",
    displayInfo: "640 × 480 IR Resolution"
  },
  {
    id: "tcc-7460",
    title: "Professional Camcorder",
    modelNumber: "TCC 7460 / TCC 742K",
    image: "/thermal-measurement/TC-4460H.png",
    displayInfo: "480 × 360 IR Resolution"
  },
  {
    id: "tcc-7660",
    title: "High-Res Camcorder",
    modelNumber: "TCC 7660 / TCC 762K",
    image: "/thermal-measurement/tcc-7660.png",
    displayInfo: "640 × 480 IR Resolution"
  },
  {
    id: "tcc-812k",
    title: "Ultra-High Res Camcorder",
    modelNumber: "TCC 812K",
    image: "/thermal-measurement/tcc-812k.png",
    displayInfo: "1280 × 1024 IR Resolution"
  }
];

// Enhanced Hero Section Component with updated buttons
const HeroSection = ({ onRequestDemo, onViewBrochure }) => {
  return (
    <div className="relative py-8 md:py-12 overflow-hidden font-['Open_Sans']">
      {/* Hero Background Elements */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-50 rounded-bl-[100px] transform -skew-x-12"></div>
        <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-10"></div>
      </div>

      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-[10%_90%] gap-0 items-center">
          {/* Left Spacer for 10% on large screens */}
          <div className="hidden lg:block"></div>
          {/* Content and Image Side by Side */}
          <div className="lg:flex lg:flex-row lg:items-center lg:gap-4">
            {/* Text Content */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="space-y-4 text-center lg:text-left lg:w-1/2"
            >
              <div className="inline-block bg-yellow-400 py-1 px-3 rounded-full mb-2">
                <span className="text-sm md:text-base font-semibold text-gray-900 font-['Open_Sans']">KRYKARD Precision Instruments</span>
              </div>
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight font-['Open_Sans']">
                THERMAL IMAGERS
              </h1>

              <p className="text-base md:text-lg lg:text-xl text-gray-900 leading-relaxed font-medium text-justify lg:text-left font-['Open_Sans']">
                Professional-grade thermal imaging solutions for accurate temperature measurement and visualization across various applications.
              </p>

              <div className="pt-2 flex flex-wrap gap-3 justify-center lg:justify-start">
                <Button
                  className="px-4 py-2 md:px-6 md:py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-semibold rounded-lg shadow-md transition duration-300 flex items-center space-x-2 font-['Open_Sans']"
                  onClick={onRequestDemo}
                >
                  <span>Request Demo</span>
                  <ArrowRight className="ml-2 h-4 w-4 md:h-5 md:w-5" />
                </Button>
                <Button
                  className="px-4 py-2 md:px-6 md:py-3 bg-white border-2 border-yellow-400 text-gray-900 font-semibold rounded-lg shadow-sm transition duration-300 hover:bg-yellow-50 flex items-center space-x-2 font-['Open_Sans']"
                  onClick={onViewBrochure}
                >
                  <span>View Brochure</span>
                  <FileText className="ml-2 h-4 w-4 md:h-5 md:w-5" />
                </Button>
              </div>
            </motion.div>

            {/* Product Image */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="flex justify-center lg:justify-center lg:w-1/2 lg:pl-8"
            >
              <div className="relative">
                <img
                  src="/thermal-measurement/thermalimager_family.png"
                  alt="Thermal Imager"
                  className="w-full max-w-md h-auto object-contain"
                />
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Key Features Section Component
const KeyFeaturesSection = () => {
  const features = [
    {
      icon: <Thermometer className="h-8 w-8 text-yellow-400" />,
      title: "Precise Temperature Measurement",
      description: "Accurate thermal imaging with professional-grade sensors"
    },
    {
      icon: <Camera className="h-8 w-8 text-yellow-400" />,
      title: "High Resolution Imaging",
      description: "Crystal clear thermal images with various resolution options"
    },
    {
      icon: <Battery className="h-8 w-8 text-yellow-400" />,
      title: "Long Battery Life",
      description: "Extended operation time for field applications"
    },
    {
      icon: <Shield className="h-8 w-8 text-yellow-400" />,
      title: "Rugged Design",
      description: "Built to withstand harsh industrial environments"
    }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Key Features</h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Our thermal imagers deliver professional-grade performance with advanced features
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="flex justify-center mb-4">
                {feature.icon}
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};



// Main Thermal Imagers Component
const ThermalImagers = () => {
  const navigate = useNavigate();

  // Handler for View Details button - Navigate to individual product pages
  const handleViewDetails = (productType: string) => {
    // Map product types to product IDs for individual pages
    const productIdMap: { [key: string]: string } = {
      'tc-s030': 'tc-s030',
      'tc-e050': 'tc-e050',
      'ma-250': 'ma-250',
      'tc-2150': 'tc-2150',
      'tc-2250': 'tc-2250',
      'tc-s240': 'tc-s240',
      'tc-3151': 'tc-3151',
      'tc-3250': 'tc-3250',
      'tc-3360': 'tc-3360',
      'tc-p360': 'tc-p360',
      'tc-4360': 'tc-4360',
      'tc-4460': 'tc-4460',
      'tc-3660': 'tc-3660',
      'tc-4660': 'tc-4660',
      'tcc-7460': 'tcc-7460',
      'tcc-7660': 'tcc-7660',
      'tcc-812k': 'tcc-812k'
    };
    const productId = productIdMap[productType];
    if (productId) {
      navigate(`/measure/thermal-imagers/product/${productId}`);
    }
  };

  const handleRequestDemo = () => {
    navigate("/contact/sales");
  };
  const handleViewBrochure = () => {
    window.open(PDF_URL, '_blank');
  };

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>
      <div className="min-h-screen bg-white">
        {/* Hero Section */}
        <HeroSection onRequestDemo={handleRequestDemo} onViewBrochure={handleViewBrochure} />

        {/* Products Section */}
        <section className="py-16 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Thermal Imager Products</h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Choose from our comprehensive range of thermal imaging solutions
              </p>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-8">
              {thermalImagers.map(product => (
                <ProductCard
                  key={product.id}
                  title={product.title}
                  modelNumber={product.modelNumber}
                  image={product.image}
                  displayInfo={product.displayInfo}
                  onViewDetailsClick={() => handleViewDetails(product.id)}
                />
              ))}
            </div>
          </div>
        </section>
        {/* Key Features Section */}
        <KeyFeaturesSection />
        {/* Contact Section with proper spacing to prevent footer overlap */}
        <div className="py-12 md:py-16 mb-8 md:mb-12 bg-gradient-to-br from-yellow-50 to-yellow-100 border-t-2 border-yellow-200">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="pb-8"
            >
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Ready to Get Started?
              </h2>
              <p className="text-base md:text-lg text-gray-700 mb-10 font-medium">
                Contact our experts to find the perfect thermal imaging solution for your needs
              </p>
              <button
                onClick={() => navigate('/contact/sales')}
                className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base mx-auto"
              >
                <Mail className="h-5 w-5" />
                <span>Contact Sales</span>
              </button>
            </motion.div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
};

export default ThermalImagers;
