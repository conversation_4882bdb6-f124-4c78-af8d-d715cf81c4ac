import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  ArrowRight,
  Gauge,
  Zap,
  Shield,
  FileText
} from "lucide-react";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

// Modern Background Component
const ModernBackground = () => {
  return (
    <div className="fixed inset-0 pointer-events-none z-[-1] overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Abstract shapes */}
      <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-yellow-100 rounded-bl-full opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-1/2 bg-yellow-200 rounded-tr-full opacity-20"></div>
      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
    </div>
  );
};

const PDF_URL = "/T&M April 2025.pdf";

// Product Overview Card Component
const ProductCard = ({
  title,
  modelNumber,
  image,
  displayInfo,
  onViewDetailsClick
}: {
  title: string;
  modelNumber: string;
  image: string;
  displayInfo: string;
  onViewDetailsClick: () => void;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className="bg-white border border-yellow-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 flex flex-col h-full"
      style={{ fontFamily: 'Open Sans, sans-serif' }}
    >
      {/* Model Number Badge */}
      <div className="flex justify-end p-3">
        <span className="bg-yellow-100 text-yellow-800 text-xs font-bold px-3 py-1 rounded-full">
          <strong>{modelNumber}</strong>
        </span>
      </div>
      {/* Product Image */}
      <div className="flex items-center justify-center h-48 md:h-56 bg-yellow-50">
        <img
          src={image}
          alt={title}
          className="max-h-full max-w-full object-contain"
          style={{ minHeight: '200px' }}
          onError={e => {
            e.currentTarget.onerror = null;
            e.currentTarget.src = 'https://via.placeholder.com/200x150/FFD700/000000?text=No+Image';
          }}
        />
      </div>
      <div className="p-4 flex-1 flex flex-col justify-between">
        <div className="text-center mb-2">
          <h3 className="text-base font-bold text-gray-900"><strong>{title}</strong></h3>
          <div className="text-xs text-gray-600 mt-1">{displayInfo}</div>
        </div>
        <button
          onClick={onViewDetailsClick}
          className="w-full bg-yellow-300 hover:bg-yellow-400 text-gray-900 font-bold py-2 px-4 rounded-lg transition-all duration-200 mt-4 flex items-center justify-center space-x-2"
        >
          <span>View Details</span>
          <ArrowRight className="inline h-4 w-4 ml-1" />
        </button>
      </div>
    </motion.div>
  );
};

// Hero Section (Oscilloscope style)
const HeroSection = ({ onRequestDemo, onViewBrochure }) => (
  <section className="relative min-h-[60vh] flex items-center justify-center py-6 md:py-12 overflow-hidden">
    {/* Background Elements */}
    <div className="absolute inset-0 z-0">
      <div className="absolute top-0 right-0 w-3/4 h-full bg-yellow-50 rounded-bl-[100px] transform -skew-x-12"></div>
      <div className="absolute bottom-20 left-0 w-64 h-64 bg-yellow-400 rounded-full opacity-10"></div>
      <div className="absolute top-20 right-20 w-32 h-32 bg-yellow-300 rounded-full opacity-20"></div>
    </div>
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10 flex flex-col md:flex-row items-center justify-center w-full gap-10 md:gap-20">
      <div className="flex-1 text-center md:text-left">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8 flex flex-col items-center md:items-start justify-center w-full"
        >
          <div className="inline-block bg-yellow-400 px-6 py-3 rounded-full mb-4">
            <span className="text-gray-900 font-bold text-lg" style={{ fontFamily: 'Open Sans, sans-serif' }}>
              <strong>KRYKARD Oscilloscope Solutions</strong>
            </span>
          </div>
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-5xl font-bold text-gray-900 leading-tight mb-4" style={{ fontFamily: 'Open Sans, sans-serif' }}>
          OSCILLOSCOPES
          </h1>
          <p className="text-base md:text-lg lg:text-lg text-black leading-relaxed font-medium max-w-2xl mx-auto md:mx-0 mb-8" style={{ fontFamily: 'Open Sans, sans-serif' }}>
            Professional-grade instruments for precision measurement of electrical signals with multiple operating modes and advanced analysis capabilities.
          </p>
          <div className="flex flex-col sm:flex-row gap-6 justify-center md:justify-start pt-2">
            <button
              className="px-6 py-3 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center space-x-3 text-base"
              onClick={onRequestDemo}
            >
              <span>Request Demo</span>
              <ArrowRight className="h-5 w-5" />
            </button>
            <button
              className="px-6 py-3 bg-white border-2 border-yellow-400 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:bg-yellow-50 flex items-center justify-center space-x-3 text-base"
              onClick={onViewBrochure}
            >
              <span>View Brochure</span>
              <FileText className="h-5 w-5" />
            </button>
          </div>
        </motion.div>
      </div>
      <div className="flex-1 flex justify-center items-center">
        <img
          src="/oscillosacopes old/OX 5042 , OX 5022.png"
          alt="Oscilloscope Hero"
          className="max-h-72 md:max-h-96 w-auto object-contain drop-shadow-2xl rounded-2xl border-4 border-yellow-300 bg-white"
        />
      </div>
    </div>
  </section>
);

// Main Oscilloscopes Component
const Oscilloscopes = () => {
  const navigate = useNavigate();

  // Handler for View Details button - Navigate to individual product pages
  const handleViewDetails = (productType: string) => {
    // Map product types to product IDs for individual pages
    const productIdMap: { [key: string]: string } = {
      'handheld': 'handheld',
      'portable': 'portable'
    };
    const productId = productIdMap[productType];
    if (productId) {
      navigate(`/measure/oscilloscopes/product/${productId}`);
    }
  };

  const handleRequestDemo = () => {
    navigate("/contact/sales");
  };
  const handleViewBrochure = () => {
    window.open(PDF_URL, '_blank');
  };

  // Product Data
  const products = [
    {
      id: "handheld",
      title: "Handheld Oscilloscope Series",
      modelNumber: "OX 5022/OX 5042",
      image: "/oscillosacopes old/OX 5042 , OX 5022.png",
      displayInfo: "20-40 MHz, 2 Isolated Channels"
    },
    {
      id: "portable",
      title: "Portable Oscilloscope Series",
      modelNumber: "OX 9062/OX 9102/OX 9104/OX 9304",
      image: "/oscilloscpoes ox 9104,9304/9J9A3371.JPG",
      displayInfo: "60-300 MHz, 2-4 Isolated Channels"
    }
  ];

  return (
    <PageLayout hideHero={true} hideBreadcrumbs={true}>
      <style>{`
        nav.mb-10 { display: none !important; }
        .py-16.xs\\:py-20.sm\\:py-24 { padding-top: 0 !important; }
      `}</style>
      <div className="min-h-screen bg-yellow-50" style={{ fontFamily: 'Open Sans, sans-serif' }}>
        {/* Hero Section */}
        <HeroSection onRequestDemo={handleRequestDemo} onViewBrochure={handleViewBrochure} />

        {/* Product Cards Section */}
        <section className="py-12 md:py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-10"
            >
              <div className="inline-block bg-yellow-100 text-yellow-800 px-6 py-3 rounded-full text-lg font-bold mb-6">
                <strong>PROFESSIONAL SERIES</strong>
              </div>
              <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                <strong>Our Oscilloscope Range</strong>
              </h2>
              <p className="text-base md:text-lg text-gray-700 max-w-4xl mx-auto font-medium mb-2">
                Professional-grade instruments for precision measurement of electrical signals with multiple operating modes
              </p>
            </motion.div>
            {/* Responsive Product Card Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 gap-8 justify-center max-w-4xl mx-auto">
              {products.map(product => (
                <ProductCard
                  key={product.id}
                  title={product.title}
                  modelNumber={product.modelNumber}
                  image={product.image}
                  displayInfo={product.displayInfo}
                  onViewDetailsClick={() => handleViewDetails(product.id)}
                />
              ))}
            </div>
          </div>
        </section>
        {/* Key Features Section */}
        <KeyFeaturesSection />
        {/* Contact Section */}
        <ContactSection onContactClick={() => navigate('/contact/sales')} />
      </div>
    </PageLayout>
  );
};

// Key Features Section (Oscilloscope style)
const KeyFeaturesSection = () => {
  const features = [
    {
      icon: <Gauge className="h-8 w-8 text-white" />,
      title: "Multi-Mode Capability",
      description: "Each device functions as an oscilloscope, multimeter, and harmonic analyzer, providing versatile measurement options in a single instrument."
    },
    {
      icon: <Zap className="h-8 w-8 text-white" />,
      title: "Isolated Channels",
      description: "Fully isolated input channels ensure accurate measurements and user safety when working with different circuit potentials."
    },
    {
      icon: <Shield className="h-8 w-8 text-white" />,
      title: "High-Resolution Display",
      description: "Crisp, vibrant displays with intuitive interfaces make complex measurements easier to visualize and interpret."
    }
  ];

  return (
    <section className="py-12 md:py-16 bg-gradient-to-br from-yellow-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
            <strong>Why Choose Our Oscilloscopes?</strong>
          </h2>
          <p className="text-base md:text-lg text-gray-700 max-w-4xl mx-auto font-medium">
            Precision-engineered instruments that combine multiple measurement capabilities in a single device
          </p>
        </motion.div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 text-center border-b-4 border-yellow-400"
            >
              <div className="flex items-center justify-center mb-4 gap-4">
                <div className="bg-gradient-to-br from-yellow-400 to-yellow-500 w-12 h-12 rounded-lg flex items-center justify-center shadow-md">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-0 text-left"><strong>{feature.title}</strong></h3>
              </div>
              <p className="text-gray-700 font-medium mt-2">{feature.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

// Contact Section Component
const ContactSection = ({ onContactClick }: { onContactClick: () => void }) => {
  return (
    <section className="py-12 md:py-16 mb-16 md:mb-24 bg-gradient-to-br from-yellow-100 to-yellow-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
            <strong>Need More Information?</strong>
          </h2>
          <p className="text-base md:text-lg text-gray-700 max-w-4xl mx-auto font-medium mb-8">
            Our team of experts is ready to help you with product specifications, custom solutions,
            pricing, and any other details you need about the KRYKARD Oscilloscopes.
          </p>
          <button
            className="inline-flex items-center px-8 py-4 bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-bold rounded-xl shadow-lg transition-all duration-300 transform hover:-translate-y-1 text-lg"
            onClick={onContactClick}
          >
            Contact Our Experts
            <ArrowRight className="ml-2 h-5 w-5" />
          </button>
        </motion.div>
      </div>
    </section>
  );
};

export default Oscilloscopes;
